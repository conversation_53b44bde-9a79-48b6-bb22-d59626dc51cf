<?php
// Simplified contact form for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "Starting contact form processing...<br>";

// Check POST data
if (empty($_POST)) {
    die('No POST data received!');
}

echo "POST data received:<br>";
foreach ($_POST as $key => $value) {
    echo "$key: " . htmlspecialchars($value) . "<br>";
}

// Check if library exists
$library_path = '../assets/vendor/php-email-form/php-email-form.php';
if (!file_exists($library_path)) {
    die('PHP Email Form library not found at: ' . $library_path);
}

echo "Including library...<br>";
include($library_path);

echo "Creating PHP_Email_Form instance...<br>";
$contact = new PHP_Email_Form();

echo "Setting up email...<br>";
$contact->ajax = true;
$contact->to = '<EMAIL>';
$contact->from_name = $_POST['name'] ?? 'Unknown';
$contact->from_email = $_POST['email'] ?? '<EMAIL>';
$contact->subject = $_POST['subject'] ?? 'Executive Briefing';

echo "Adding messages...<br>";
$contact->add_message($_POST['name'] ?? 'Unknown', 'From');
$contact->add_message($_POST['email'] ?? '<EMAIL>', 'Email');
$contact->add_message($contact->subject, 'Subject');

if (!empty($_POST['phone'])) {
    $contact->add_message($_POST['phone'], 'Phone');
}

$contact->add_message($_POST['message'] ?? 'No message', 'Message', 10);

echo "Attempting to send email...<br>";
try {
    $result = $contact->send();
    echo "Send result: " . $result;
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
