<?php
// Simplified contact form for debugging with logging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Create log function
function writeLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents('contact_debug.log', $logMessage, FILE_APPEND | LOCK_EX);
}

writeLog("=== Contact form processing started ===");
echo "Starting contact form processing...<br>";

try {
    // Check POST data
    if (empty($_POST)) {
        writeLog("ERROR: No POST data received");
        die('No POST data received!');
    }

    writeLog("POST data received: " . print_r($_POST, true));
    echo "POST data received:<br>";
    foreach ($_POST as $key => $value) {
        echo "$key: " . htmlspecialchars($value) . "<br>";
    }

    // Check if library exists
    $library_path = '../assets/vendor/php-email-form/php-email-form.php';
    writeLog("Checking for library at: $library_path");
    
    if (!file_exists($library_path)) {
        writeLog("ERROR: PHP Email Form library not found at: $library_path");
        die('PHP Email Form library not found at: ' . $library_path);
    }

    writeLog("Library found, including...");
    echo "Including library...<br>";
    include($library_path);

    writeLog("Creating PHP_Email_Form instance...");
    echo "Creating PHP_Email_Form instance...<br>";
    $contact = new PHP_Email_Form();

    writeLog("Setting up email configuration...");
    echo "Setting up email...<br>";
    $contact->ajax = true;
    $contact->to = '<EMAIL>';
    $contact->from_name = $_POST['name'] ?? 'Unknown';
    $contact->from_email = $_POST['email'] ?? '<EMAIL>';
    $contact->subject = $_POST['subject'] ?? 'Executive Briefing';

    writeLog("Email config - To: <EMAIL>, From: " . $contact->from_name . " <" . $contact->from_email . ">, Subject: " . $contact->subject);

    writeLog("Adding messages...");
    echo "Adding messages...<br>";
    $contact->add_message($_POST['name'] ?? 'Unknown', 'From');
    $contact->add_message($_POST['email'] ?? '<EMAIL>', 'Email');
    $contact->add_message($contact->subject, 'Subject');

    if (!empty($_POST['phone'])) {
        $contact->add_message($_POST['phone'], 'Phone');
        writeLog("Added phone: " . $_POST['phone']);
    }

    $contact->add_message($_POST['message'] ?? 'No message', 'Message', 10);
    writeLog("All messages added successfully");

    writeLog("Attempting to send email...");
    echo "Attempting to send email...<br>";
    
    $result = $contact->send();
    writeLog("Send result: " . $result);
    echo "Send result: " . $result;

} catch (Exception $e) {
    $error = "Exception caught: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
    writeLog("ERROR: " . $error);
    echo "Error: " . $e->getMessage();
} catch (Error $e) {
    $error = "Fatal error caught: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
    writeLog("FATAL ERROR: " . $error);
    echo "Fatal Error: " . $e->getMessage();
}

writeLog("=== Contact form processing ended ===");
?>
