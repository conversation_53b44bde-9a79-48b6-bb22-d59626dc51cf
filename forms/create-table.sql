-- S<PERSON> to create the contact submissions table
-- Run this in your MySQL database

CREATE TABLE IF NOT EXISTS contact_submissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    subject <PERSON><PERSON><PERSON><PERSON>(255),
    message TEXT NOT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_submitted_at (submitted_at)
);

-- Optional: Create a simple admin view
-- You can access this via forms/view-submissions.php
