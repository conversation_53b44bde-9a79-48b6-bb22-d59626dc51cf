<?php
// Simple admin page to view contact form submissions
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration - UPDATE THESE WITH YOUR ACTUAL DATABASE DETAILS
$db_host = 'localhost';  // Your database host (usually localhost)
$db_name = '';           // Your actual database name
$db_user = '';           // Your actual database username
$db_pass = '';           // Your actual database password

try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Get submissions
$sql = "SELECT * FROM contact_submissions ORDER BY submitted_at DESC LIMIT 50";
$stmt = $pdo->query($sql);
$submissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Submissions</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .message { max-width: 300px; word-wrap: break-word; }
        .date { white-space: nowrap; }
    </style>
</head>
<body>
    <h1>Contact Form Submissions</h1>
    <p>Total submissions: <?php echo count($submissions); ?></p>
    
    <?php if (empty($submissions)): ?>
        <p>No submissions found.</p>
    <?php else: ?>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Date</th>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Phone</th>
                    <th>Subject</th>
                    <th>Message</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($submissions as $sub): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($sub['id']); ?></td>
                        <td class="date"><?php echo htmlspecialchars($sub['submitted_at']); ?></td>
                        <td><?php echo htmlspecialchars($sub['name']); ?></td>
                        <td><?php echo htmlspecialchars($sub['email']); ?></td>
                        <td><?php echo htmlspecialchars($sub['phone']); ?></td>
                        <td><?php echo htmlspecialchars($sub['subject']); ?></td>
                        <td class="message"><?php echo nl2br(htmlspecialchars($sub['message'])); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php endif; ?>
</body>
</html>
