<?php
  // Contact form with alternative SMTP settings
  error_reporting(E_ALL);
  ini_set('display_errors', 1);

  function writeLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents('contact_debug.log', $logMessage, FILE_APPEND | LOCK_EX);
  }

  writeLog("=== Contact form with alternative SMTP ===");

  $receiving_email_address = '<EMAIL>';

  if( file_exists($php_email_form = '../assets/vendor/php-email-form/php-email-form.php' )) {
    include( $php_email_form );
  } else {
    die( 'Unable to load the "PHP Email Form" Library!');
  }

  if (empty($_POST)) {
    die('No POST data received!');
  }

  if (!isset($_POST['name']) || !isset($_POST['email']) || !isset($_POST['message'])) {
    die('Required fields are missing!');
  }

  $contact = new PHP_Email_Form;
  $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
  $contact->ajax = $isAjax;

  $contact->to = $receiving_email_address;
  $contact->from_name = $_POST['name'];
  $contact->from_email = $_POST['email'];
  $contact->subject = !empty($_POST['subject']) ? $_POST['subject'] : 'Executive Briefing';

  // Alternative SMTP settings - try port 587 with TLS
  $contact->smtp = array(
    'host' => 'smtp.hostinger.com',
    'username' => '<EMAIL>',
    'password' => 'Techn0!oGy',
    'port' => '587',
    'encryption' => 'tls',
    'timeout' => 5
  );

  $contact->add_message( $_POST['name'], 'From');
  $contact->add_message( $_POST['email'], 'Email');
  $contact->add_message( $contact->subject, 'Subject');
  if(isset($_POST['phone']) && !empty($_POST['phone'])) {
    $contact->add_message( $_POST['phone'], 'Phone');
  }
  $contact->add_message( $_POST['message'], 'Message', 10);

  try {
    writeLog("Attempting to send with port 587/TLS...");
    $result = $contact->send();
    writeLog("Send result: " . $result);
    
    if ($isAjax) {
      echo $result;
    } else {
      if ($result == 'OK') {
        echo "<h2>Success!</h2><p>Your message has been sent successfully!</p>";
      } else {
        echo "<h2>Error</h2><p>" . htmlspecialchars($result) . "</p>";
      }
      echo "<a href='../index.html'>Back to website</a>";
    }
  } catch (Exception $e) {
    writeLog("ERROR: " . $e->getMessage());
    echo $isAjax ? $e->getMessage() : "<h2>Error</h2><p>" . htmlspecialchars($e->getMessage()) . "</p>";
  }
?>
