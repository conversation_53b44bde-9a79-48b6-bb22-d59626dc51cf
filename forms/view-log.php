<?php
// Simple script to view the debug log
echo "<h2>Contact Form Debug Log</h2>";
echo "<style>body{font-family:monospace;} .log{background:#f5f5f5;padding:10px;border:1px solid #ccc;white-space:pre-wrap;}</style>";

$logFile = 'contact_debug.log';

if (file_exists($logFile)) {
    echo "<div class='log'>";
    echo htmlspecialchars(file_get_contents($logFile));
    echo "</div>";
    
    echo "<br><a href='?clear=1'>Clear Log</a>";
    
    // Clear log if requested
    if (isset($_GET['clear'])) {
        file_put_contents($logFile, '');
        echo "<br><strong>Log cleared!</strong>";
        echo "<script>setTimeout(function(){window.location.href='view-log.php';}, 1000);</script>";
    }
} else {
    echo "<p>No log file found yet. Submit the contact form to generate logs.</p>";
}

echo "<br><br><a href='../index.html'>Back to Contact Form</a>";
?>
