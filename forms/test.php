<?php
// Simple test script to check if <PERSON><PERSON> is working
echo "P<PERSON> is working!<br>";
echo "Current directory: " . getcwd() . "<br>";
echo "PHP version: " . phpversion() . "<br>";

// Check if the email form library exists
$library_path = '../assets/vendor/php-email-form/php-email-form.php';
echo "Looking for library at: " . realpath($library_path) . "<br>";

if (file_exists($library_path)) {
    echo "✓ PHP Email Form library found<br>";
    try {
        include($library_path);
        echo "✓ Library included successfully<br>";
        
        // Try to create an instance
        $test = new PHP_Email_Form();
        echo "✓ PHP_Email_Form class instantiated successfully<br>";
    } catch (Exception $e) {
        echo "✗ Error including library: " . $e->getMessage() . "<br>";
    }
} else {
    echo "✗ PHP Email Form library NOT found<br>";
    echo "Files in ../assets/vendor/php-email-form/:<br>";
    if (is_dir('../assets/vendor/php-email-form/')) {
        $files = scandir('../assets/vendor/php-email-form/');
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                echo "- $file<br>";
            }
        }
    } else {
        echo "Directory does not exist<br>";
    }
}

// Check POST data if any
if (!empty($_POST)) {
    echo "<br>POST data received:<br>";
    foreach ($_POST as $key => $value) {
        echo "$key: " . htmlspecialchars($value) . "<br>";
    }
} else {
    echo "<br>No POST data received<br>";
}
?>
