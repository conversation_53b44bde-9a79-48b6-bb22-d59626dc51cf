<?php
  /**
  * Requires the "PHP Email Form" library
  * The "PHP Email Form" library is available only in the pro version of the template
  * The library should be uploaded to: vendor/php-email-form/php-email-form.php
  * For more info and help: https://bootstrapmade.com/php-email-form/
  */

  // Enable error reporting for debugging
  error_reporting(E_ALL);
  ini_set('display_errors', 1);

  // Replace <EMAIL> with your real receiving email address
  $receiving_email_address = '<EMAIL>';

  if( file_exists($php_email_form = '../assets/vendor/php-email-form/php-email-form.php' )) {
    include( $php_email_form );
  } else {
    die( 'Unable to load the "PHP Email Form" Library!');
  }

  // Debug: Check what POST data we received
  if (empty($_POST)) {
    die('No POST data received!');
  }

  // Check if required fields are present
  if (!isset($_POST['name']) || !isset($_POST['email']) || !isset($_POST['message'])) {
    die('Required fields are missing! Received: ' . print_r($_POST, true));
  }

  $contact = new PHP_Email_Form;
  $contact->ajax = true;

  $contact->to = $receiving_email_address;
  $contact->from_name = $_POST['name'];
  $contact->from_email = $_POST['email'];

  // Set subject - use default if not provided or if field is disabled
  $contact->subject = !empty($_POST['subject']) ? $_POST['subject'] : 'Executive Briefing';

  // Uncomment below code if you want to use SMTP to send emails. You need to enter your correct SMTP credentials
  $contact->smtp = array(
    'host' => 'smtp.hostinger.com',
    'username' => '<EMAIL>',
    'password' => 'Techn0!oGy',
    'port' => '465'
  );

  $contact->add_message( $_POST['name'], 'From');
  $contact->add_message( $_POST['email'], 'Email');
  $contact->add_message( $contact->subject, 'Subject');
  if(isset($_POST['phone']) && !empty($_POST['phone'])) {
    $contact->add_message( $_POST['phone'], 'Phone');
  }
  $contact->add_message( $_POST['message'], 'Message', 10);

  try {
    echo $contact->send();
  } catch (Exception $e) {
    die('Error sending email: ' . $e->getMessage());
  }
?>
