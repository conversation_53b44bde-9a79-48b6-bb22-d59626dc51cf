<?php
  /**
  * Requires the "PHP Email Form" library
  * The "PHP Email Form" library is available only in the pro version of the template
  * The library should be uploaded to: vendor/php-email-form/php-email-form.php
  * For more info and help: https://bootstrapmade.com/php-email-form/
  */

  // Enable error reporting for debugging
  error_reporting(E_ALL);
  ini_set('display_errors', 1);

  // Create log function
  function writeLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    file_put_contents('contact_debug.log', $logMessage, FILE_APPEND | LOCK_EX);
  }

  writeLog("=== Contact form processing started (original) ===");
  writeLog("Request method: " . $_SERVER['REQUEST_METHOD']);
  writeLog("Content type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
  writeLog("HTTP headers: " . print_r(getallheaders(), true));

  // Replace <EMAIL> with your real receiving email address
  $receiving_email_address = '<EMAIL>';

  if( file_exists($php_email_form = '../assets/vendor/php-email-form/php-email-form.php' )) {
    writeLog("Including PHP Email Form library");
    include( $php_email_form );
  } else {
    writeLog("ERROR: Unable to load PHP Email Form Library at: $php_email_form");
    die( 'Unable to load the "PHP Email Form" Library!');
  }

  // Debug: Check what POST data we received
  writeLog("POST array: " . print_r($_POST, true));
  writeLog("FILES array: " . print_r($_FILES, true));
  writeLog("Raw input: " . file_get_contents('php://input'));

  if (empty($_POST)) {
    writeLog("ERROR: No POST data received");
    die('No POST data received!');
  }

  writeLog("POST data received: " . print_r($_POST, true));

  // Check if required fields are present
  if (!isset($_POST['name']) || !isset($_POST['email']) || !isset($_POST['message'])) {
    writeLog("ERROR: Required fields missing. POST data: " . print_r($_POST, true));
    die('Required fields are missing! Received: ' . print_r($_POST, true));
  }

  $contact = new PHP_Email_Form;

  // Check if this is an AJAX request
  $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
  writeLog("Is AJAX request: " . ($isAjax ? 'yes' : 'no'));

  $contact->ajax = $isAjax;

  $contact->to = $receiving_email_address;
  $contact->from_name = $_POST['name'];
  $contact->from_email = $_POST['email'];

  // Set subject - use default if not provided or if field is disabled
  $contact->subject = !empty($_POST['subject']) ? $_POST['subject'] : 'Executive Briefing';

  // SMTP configuration with timeout settings
  $contact->smtp = array(
    'host' => 'smtp.hostinger.com',
    'username' => '<EMAIL>',
    'password' => 'Techn0!oGy',
    'port' => '465',
    'encryption' => 'ssl',
    'timeout' => 10
  );

  $contact->add_message( $_POST['name'], 'From');
  $contact->add_message( $_POST['email'], 'Email');
  $contact->add_message( $contact->subject, 'Subject');
  if(isset($_POST['phone']) && !empty($_POST['phone'])) {
    $contact->add_message( $_POST['phone'], 'Phone');
  }
  $contact->add_message( $_POST['message'], 'Message', 10);

  try {
    writeLog("Attempting to send email...");
    $result = $contact->send();
    writeLog("Send result: " . $result);

    if ($isAjax) {
      // For AJAX requests, just echo the result
      echo $result;
    } else {
      // For regular form submissions, show a user-friendly page
      if ($result == 'OK') {
        echo "<h2>Success!</h2><p>Your message has been sent successfully!</p>";
        echo "<a href='../index.html'>Back to website</a>";
      } else {
        echo "<h2>Error</h2><p>There was an error sending your message: " . htmlspecialchars($result) . "</p>";
        echo "<a href='../index.html'>Back to website</a>";
      }
    }
  } catch (Exception $e) {
    $error = "Exception: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
    writeLog("ERROR: " . $error);

    if ($isAjax) {
      echo 'Error sending email: ' . $e->getMessage();
    } else {
      echo "<h2>Error</h2><p>Error sending email: " . htmlspecialchars($e->getMessage()) . "</p>";
      echo "<a href='../index.html'>Back to website</a>";
    }
  } catch (Error $e) {
    $error = "Fatal error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine();
    writeLog("FATAL ERROR: " . $error);

    if ($isAjax) {
      echo 'Fatal error: ' . $e->getMessage();
    } else {
      echo "<h2>Fatal Error</h2><p>Fatal error: " . htmlspecialchars($e->getMessage()) . "</p>";
      echo "<a href='../index.html'>Back to website</a>";
    }
  }

  writeLog("=== Contact form processing ended (original) ===");
?>
