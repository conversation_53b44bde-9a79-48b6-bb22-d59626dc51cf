<?php
// Simple contact form - sends email and stores in database
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$db_host = 'localhost';
$db_name = 'u548077102_kaastech';
$db_user = 'u548077102_kaasadmin';
$db_pass = 'Az:m:ke/Dc:0';

// Email configuration
$to_email = '<EMAIL>';
$from_email = '<EMAIL>';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('Invalid request method');
}

// Get form data
$name = $_POST['name'] ?? '';
$email = $_POST['email'] ?? '';
$phone = $_POST['phone'] ?? '';
$subject = $_POST['subject'] ?? 'Executive Briefing';
$message = $_POST['message'] ?? '';

// Validate required fields
if (empty($name) || empty($email) || empty($message)) {
    die('Please fill in all required fields (Name, Email, Message)');
}

// Validate email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    die('Please enter a valid email address');
}

// Connect to database
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Insert into database
try {
    $sql = "INSERT INTO contact_submissions (name, email, phone, subject, message, submitted_at) 
            VALUES (:name, :email, :phone, :subject, :message, NOW())";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':name' => $name,
        ':email' => $email,
        ':phone' => $phone,
        ':subject' => $subject,
        ':message' => $message
    ]);
    
    $submission_id = $pdo->lastInsertId();
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}

// Send email
$email_subject = "New Contact Form Submission: $subject";
$email_body = "
New contact form submission received:

Name: $name
Email: $email
Phone: $phone
Subject: $subject

Message:
$message

---
Submission ID: $submission_id
Submitted: " . date('Y-m-d H:i:s') . "
";

$headers = [
    'From: ' . $from_email,
    'Reply-To: ' . $email,
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
];

$email_sent = mail($to_email, $email_subject, $email_body, implode("\r\n", $headers));

// Check if this is an AJAX request
$isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($isAjax) {
    // For AJAX requests, return simple response
    if ($email_sent) {
        echo 'OK';
    } else {
        echo 'Email sending failed, but your message was saved (ID: #' . $submission_id . ')';
    }
} else {
    // For regular form submissions, redirect back
    if ($email_sent) {
        header('Location: ../index.html?success=1&id=' . $submission_id);
    } else {
        header('Location: ../index.html?error=1&id=' . $submission_id);
    }
    exit;
}
