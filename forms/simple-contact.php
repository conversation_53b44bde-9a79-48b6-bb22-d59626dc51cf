<?php
// Simple contact form - sends email and stores in database
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$db_host = 'localhost';
$db_name = 'u548077102_kaastech';
$db_user = 'u548077102_kaasadmin';
$db_pass = 'Az:m:ke/Dc:0';

// Email configuration
$to_email = '<EMAIL>';
$from_email = '<EMAIL>';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    die('Invalid request method');
}

// Get form data
$name = $_POST['name'] ?? '';
$email = $_POST['email'] ?? '';
$phone = $_POST['phone'] ?? '';
$subject = $_POST['subject'] ?? 'Executive Briefing';
$message = $_POST['message'] ?? '';

// Validate required fields
if (empty($name) || empty($email) || empty($message)) {
    die('Please fill in all required fields (Name, Email, Message)');
}

// Validate email
if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    die('Please enter a valid email address');
}

// Connect to database
try {
    $pdo = new PDO("mysql:host=$db_host;dbname=$db_name;charset=utf8", $db_user, $db_pass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    die('Database connection failed: ' . $e->getMessage());
}

// Insert into database
try {
    $sql = "INSERT INTO contact_submissions (name, email, phone, subject, message, submitted_at) 
            VALUES (:name, :email, :phone, :subject, :message, NOW())";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        ':name' => $name,
        ':email' => $email,
        ':phone' => $phone,
        ':subject' => $subject,
        ':message' => $message
    ]);
    
    $submission_id = $pdo->lastInsertId();
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}

// Send email
$email_subject = "New Contact Form Submission: $subject";
$email_body = "
New contact form submission received:

Name: $name
Email: $email
Phone: $phone
Subject: $subject

Message:
$message

---
Submission ID: $submission_id
Submitted: " . date('Y-m-d H:i:s') . "
";

$headers = [
    'From: ' . $from_email,
    'Reply-To: ' . $email,
    'X-Mailer: PHP/' . phpversion(),
    'Content-Type: text/plain; charset=UTF-8'
];

$email_sent = mail($to_email, $email_subject, $email_body, implode("\r\n", $headers));

// Show result
?>
<!DOCTYPE html>
<html>
<head>
    <title>Contact Form Result</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .success { color: #28a745; background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; }
        .error { color: #721c24; background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; }
        .back-link { margin-top: 20px; }
        .back-link a { color: #007bff; text-decoration: none; }
        .back-link a:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <?php if ($email_sent): ?>
        <div class="success">
            <h2>✓ Message Sent Successfully!</h2>
            <p>Thank you for contacting us. Your message has been received and stored.</p>
            <p><strong>Submission ID:</strong> #<?php echo $submission_id; ?></p>
            <p>We will get back to you as soon as possible.</p>
        </div>
    <?php else: ?>
        <div class="error">
            <h2>⚠ Email Sending Failed</h2>
            <p>Your message was saved to our database (ID: #<?php echo $submission_id; ?>) but we couldn't send the email notification.</p>
            <p>We will still review your message and get back to you.</p>
        </div>
    <?php endif; ?>
    
    <div class="back-link">
        <a href="../index.html">← Back to Website</a>
    </div>
</body>
</html>
